from rest_framework.generics import CreateAPI<PERSON>iew, ListAPIView
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from tenants.models import PendingClient, Client, Domain
from .serializers import PendingClientSerializer
from restaurant.api.serializers import RestaurantSerializer
from rest_framework.permissions import AllowAny

class RegisterRestaurantView(CreateAPIView):
    queryset = PendingClient.objects.all()
    serializer_class = PendingClientSerializer
    permission_classes = [AllowAny]

class FeaturedRestaurantsView(ListAPIView):
    """
    Public endpoint to fetch featured/active restaurants for the landing page
    """
    serializer_class = RestaurantSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        # Return active restaurants, limit to featured ones
        return Client.objects.filter(active=True).order_by('-created_on')[:6]

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        # Transform data to match frontend expectations
        featured_restaurants = []
        for restaurant in serializer.data:
            featured_restaurants.append({
                'id': restaurant.get('id'),
                'name': restaurant.get('restaurant_name'),
                'cuisine': 'Malawian',  # Default for now
                'rating': 4.5,  # Default rating
                'reviews': 100,  # Default review count
                'distance': '1.0 km',  # Default distance
                'image': restaurant.get('logo') or '/placeholder.svg?height=200&width=300',
                'deliveryTime': '25-35 min',  # Default delivery time
                'featured': True,
                'address': restaurant.get('address'),
                'description': restaurant.get('description'),
                'phone': restaurant.get('phone_number'),
            })

        return Response(featured_restaurants, status=status.HTTP_200_OK)


class ResolveTenantView(APIView):
    """
    Public endpoint to resolve restaurant ID (schema_name) to tenant domain
    Used by manager login to find the correct tenant subdomain
    """
    permission_classes = [AllowAny]

    def options(self, request, *args, **kwargs):
        """Handle CORS preflight requests"""
        return Response(status=status.HTTP_200_OK)

    def post(self, request, *args, **kwargs):
        restaurant_id = request.data.get('restaurant_id')

        if not restaurant_id:
            return Response(
                {"error": "restaurant_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # For testing purposes, let's add a test case
        if restaurant_id == "test-restaurant":
            return Response({
                'domain': 'test-restaurant.localhost',
                'restaurant_name': 'Test Restaurant',
                'schema_name': 'test_restaurant'
            }, status=status.HTTP_200_OK)

        try:
            # Find the client by schema_name (restaurant_id)
            client = Client.objects.get(schema_name=restaurant_id, active=True)

            # Get the primary domain for this client
            domain = Domain.objects.get(tenant=client, is_primary=True, is_active=True)

            return Response({
                'domain': domain.domain,
                'restaurant_name': client.restaurant_name,
                'schema_name': client.schema_name
            }, status=status.HTTP_200_OK)

        except Client.DoesNotExist:
            return Response(
                {"error": f"Restaurant '{restaurant_id}' not found or not active"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Domain.DoesNotExist:
            return Response(
                {"error": "Domain not configured for this restaurant"},
                status=status.HTTP_404_NOT_FOUND
            )


class ManagerAuthView(APIView):
    """
    Public endpoint for manager authentication that handles tenant switching
    """
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        restaurant_id = request.data.get('restaurant_id')
        username = request.data.get('username')
        password = request.data.get('password')

        if not all([restaurant_id, username, password]):
            return Response(
                {"error": "restaurant_id, username, and password are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Find the client by schema_name (restaurant_id)
            client = Client.objects.get(schema_name=restaurant_id, active=True)

            # Import here to avoid circular imports
            from django_tenants.utils import tenant_context
            from rest_framework.authtoken.models import Token

            # Switch to tenant schema and authenticate
            with tenant_context(client):
                # Try to authenticate the user in the tenant schema
                from django.contrib.auth import get_user_model
                User = get_user_model()

                try:
                    user = User.objects.get(username=username)
                    if user.check_password(password) and user.role == 'manager':
                        # Create or get token for the user
                        token, created = Token.objects.get_or_create(user=user)

                        return Response({
                            'token': token.key,
                            'user_id': user.pk,
                            'email': user.email,
                            'restaurant_name': client.restaurant_name,
                            'schema_name': client.schema_name
                        }, status=status.HTTP_200_OK)
                    else:
                        return Response(
                            {"error": "Invalid credentials or insufficient permissions"},
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                except User.DoesNotExist:
                    return Response(
                        {"error": "Invalid credentials"},
                        status=status.HTTP_401_UNAUTHORIZED
                    )

        except Client.DoesNotExist:
            return Response(
                {"error": f"Restaurant '{restaurant_id}' not found or not active"},
                status=status.HTTP_404_NOT_FOUND
            )


class ManagerAuthView(APIView):
    """
    Public endpoint for manager authentication that handles tenant switching
    """
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        restaurant_id = request.data.get('restaurant_id')
        username = request.data.get('username')
        password = request.data.get('password')

        if not all([restaurant_id, username, password]):
            return Response(
                {"error": "restaurant_id, username, and password are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Find the client by schema_name (restaurant_id)
            client = Client.objects.get(schema_name=restaurant_id, active=True)

            # Import here to avoid circular imports
            from django_tenants.utils import tenant_context
            from rest_framework.authtoken.models import Token

            # Switch to tenant schema and authenticate
            with tenant_context(client):
                # Try to authenticate the user in the tenant schema
                from django.contrib.auth import get_user_model
                User = get_user_model()

                try:
                    user = User.objects.get(username=username)
                    if user.check_password(password) and user.role == 'manager':
                        # Create or get token for the user
                        token, created = Token.objects.get_or_create(user=user)

                        return Response({
                            'token': token.key,
                            'user_id': user.pk,
                            'email': user.email,
                            'restaurant_name': client.restaurant_name,
                            'schema_name': client.schema_name
                        }, status=status.HTTP_200_OK)
                    else:
                        return Response(
                            {"error": "Invalid credentials or insufficient permissions"},
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                except User.DoesNotExist:
                    return Response(
                        {"error": "Invalid credentials"},
                        status=status.HTTP_401_UNAUTHORIZED
                    )

        except Client.DoesNotExist:
            return Response(
                {"error": f"Restaurant '{restaurant_id}' not found or not active"},
                status=status.HTTP_404_NOT_FOUND
            )

