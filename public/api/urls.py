from django.urls import path, include
from public.api.views import RegisterRestaurantView, FeaturedRestaurantsView, ResolveTenantView, ManagerAuthView
from tenants.views import tenant_home


urlpatterns = [
    path('', tenant_home, name='tenant-home'),
    path('register-restaurant/', RegisterRestaurantView.as_view(), name='restaurant-register'),
    path('featured-restaurants/', FeaturedRestaurantsView.as_view(), name='featured-restaurants'),
    path('resolve-tenant/', ResolveTenantView.as_view(), name='resolve-tenant'),
    path('manager-auth/', ManagerAuthView.as_view(), name='manager-auth'),
    path('customer/', include('users.api.public')),
]
