"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Edit, Trash2, ChefHat, Tag } from "lucide-react";
import { toast } from "sonner";

export default function MenuManagementPage() {
  const [categories, setCategories] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);
  const [isAddItemOpen, setIsAddItemOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingItem, setEditingItem] = useState(null);

  // Form states
  const [newCategory, setNewCategory] = useState({
    name: "",
    description: "",
    order: 0,
  });

  const [newItem, setNewItem] = useState({
    name: "",
    description: "",
    price: "",
    category_id: "",
    preparation_time: "",
    is_vegetarian: false,
    is_vegan: false,
    is_glutten_free: false,
    calories: "",
  });

  // Fetch data from backend
  useEffect(() => {
    fetchCategories();
    fetchMenuItems();
  }, []);

  const getAuthHeaders = () => {
    const token = localStorage.getItem("accessToken");
    return {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch(
        "http://localhost:8000/api/menu/categories/",
        {
          headers: getAuthHeaders(),
        }
      );
      if (response.ok) {
        const data = await response.json();
        setCategories(data.results || data);
      }
    } catch (error) {
      toast.error("Failed to fetch categories");
    }
  };

  const fetchMenuItems = async () => {
    try {
      const response = await fetch(
        "http://localhost:8000/api/menu/menu-items/",
        {
          headers: getAuthHeaders(),
        }
      );
      if (response.ok) {
        const data = await response.json();
        setMenuItems(data.results || data);
      }
    } catch (error) {
      toast.error("Failed to fetch menu items");
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategory = async () => {
    try {
      const response = await fetch(
        "http://localhost:8000/api/menu/categories/",
        {
          method: "POST",
          headers: getAuthHeaders(),
          body: JSON.stringify(newCategory),
        }
      );

      if (response.ok) {
        toast.success("Category added successfully");
        setIsAddCategoryOpen(false);
        setNewCategory({ name: "", description: "", order: 0 });
        fetchCategories();
      } else {
        toast.error("Failed to add category");
      }
    } catch (error) {
      toast.error("Network error");
    }
  };

  const handleAddMenuItem = async () => {
    try {
      const itemData = {
        ...newItem,
        price: parseFloat(newItem.price),
        preparation_time: parseInt(newItem.preparation_time),
        calories: newItem.calories ? parseInt(newItem.calories) : null,
      };

      const response = await fetch(
        "http://localhost:8000/api/menu/menu-items/",
        {
          method: "POST",
          headers: getAuthHeaders(),
          body: JSON.stringify(itemData),
        }
      );

      if (response.ok) {
        toast.success("Menu item added successfully");
        setIsAddItemOpen(false);
        setNewItem({
          name: "",
          description: "",
          price: "",
          category_id: "",
          preparation_time: "",
          is_vegetarian: false,
          is_vegan: false,
          is_glutten_free: false,
          calories: "",
        });
        fetchMenuItems();
      } else {
        toast.error("Failed to add menu item");
      }
    } catch (error) {
      toast.error("Network error");
    }
  };

  const handleDeleteCategory = async (id) => {
    if (!confirm("Are you sure you want to delete this category?")) return;

    try {
      const response = await fetch(
        `http://localhost:8000/api/menu/categories/${id}/`,
        {
          method: "DELETE",
          headers: getAuthHeaders(),
        }
      );

      if (response.ok) {
        toast.success("Category deleted successfully");
        fetchCategories();
      } else {
        toast.error("Failed to delete category");
      }
    } catch (error) {
      toast.error("Network error");
    }
  };

  const handleDeleteMenuItem = async (id) => {
    if (!confirm("Are you sure you want to delete this menu item?")) return;

    try {
      const response = await fetch(
        `http://localhost:8000/api/menu/menu-items/${id}/`,
        {
          method: "DELETE",
          headers: getAuthHeaders(),
        }
      );

      if (response.ok) {
        toast.success("Menu item deleted successfully");
        fetchMenuItems();
      } else {
        toast.error("Failed to delete menu item");
      }
    } catch (error) {
      toast.error("Network error");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading menu data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Menu Management
          </h1>
          <p className="text-gray-600">
            Manage your restaurant's menu categories and items
          </p>
        </div>

        <Tabs defaultValue="categories" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="items">Menu Items</TabsTrigger>
          </TabsList>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Tag className="h-5 w-5" />
                      Categories
                    </CardTitle>
                    <CardDescription>
                      Organize your menu into categories
                    </CardDescription>
                  </div>
                  <Dialog
                    open={isAddCategoryOpen}
                    onOpenChange={setIsAddCategoryOpen}
                  >
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Category
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add New Category</DialogTitle>
                        <DialogDescription>
                          Create a new menu category to organize your items
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="category-name">Category Name</Label>
                          <Input
                            id="category-name"
                            value={newCategory.name}
                            onChange={(e) =>
                              setNewCategory({
                                ...newCategory,
                                name: e.target.value,
                              })
                            }
                            placeholder="e.g., Appetizers, Main Courses"
                          />
                        </div>
                        <div>
                          <Label htmlFor="category-description">
                            Description
                          </Label>
                          <Textarea
                            id="category-description"
                            value={newCategory.description}
                            onChange={(e) =>
                              setNewCategory({
                                ...newCategory,
                                description: e.target.value,
                              })
                            }
                            placeholder="Brief description of this category"
                          />
                        </div>
                        <div>
                          <Label htmlFor="category-order">Display Order</Label>
                          <Input
                            id="category-order"
                            type="number"
                            value={newCategory.order}
                            onChange={(e) =>
                              setNewCategory({
                                ...newCategory,
                                order: parseInt(e.target.value) || 0,
                              })
                            }
                            placeholder="0"
                          />
                        </div>
                        <Button onClick={handleAddCategory} className="w-full">
                          Add Category
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {categories.map((category) => (
                    <Card
                      key={category.id}
                      className="border-l-4 border-l-orange-500"
                    >
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-lg">
                            {category.name}
                          </h3>
                          <div className="flex gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setEditingCategory(category)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteCategory(category.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <p className="text-gray-600 text-sm mb-2">
                          {category.description}
                        </p>
                        <div className="flex justify-between items-center">
                          <Badge variant="secondary">
                            Order: {category.order}
                          </Badge>
                          <Badge
                            variant={
                              category.is_active ? "default" : "secondary"
                            }
                          >
                            {category.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Menu Items Tab */}
          <TabsContent value="items" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <ChefHat className="h-5 w-5" />
                      Menu Items
                    </CardTitle>
                    <CardDescription>
                      Manage your restaurant's menu items
                    </CardDescription>
                  </div>
                  <Dialog open={isAddItemOpen} onOpenChange={setIsAddItemOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Menu Item
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Add New Menu Item</DialogTitle>
                        <DialogDescription>
                          Add a new dish to your menu
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 max-h-96 overflow-y-auto">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="item-name">Item Name</Label>
                            <Input
                              id="item-name"
                              value={newItem.name}
                              onChange={(e) =>
                                setNewItem({ ...newItem, name: e.target.value })
                              }
                              placeholder="e.g., Grilled Chicken"
                            />
                          </div>
                          <div>
                            <Label htmlFor="item-price">Price (MWK)</Label>
                            <Input
                              id="item-price"
                              type="number"
                              step="0.01"
                              value={newItem.price}
                              onChange={(e) =>
                                setNewItem({
                                  ...newItem,
                                  price: e.target.value,
                                })
                              }
                              placeholder="0.00"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="item-description">Description</Label>
                          <Textarea
                            id="item-description"
                            value={newItem.description}
                            onChange={(e) =>
                              setNewItem({
                                ...newItem,
                                description: e.target.value,
                              })
                            }
                            placeholder="Describe the dish..."
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="item-category">Category</Label>
                            <Select
                              value={newItem.category_id}
                              onValueChange={(value) =>
                                setNewItem({ ...newItem, category_id: value })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                              <SelectContent>
                                {categories.map((category) => (
                                  <SelectItem
                                    key={category.id}
                                    value={category.id.toString()}
                                  >
                                    {category.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="prep-time">
                              Preparation Time (minutes)
                            </Label>
                            <Input
                              id="prep-time"
                              type="number"
                              value={newItem.preparation_time}
                              onChange={(e) =>
                                setNewItem({
                                  ...newItem,
                                  preparation_time: e.target.value,
                                })
                              }
                              placeholder="15"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="calories">Calories (optional)</Label>
                          <Input
                            id="calories"
                            type="number"
                            value={newItem.calories}
                            onChange={(e) =>
                              setNewItem({
                                ...newItem,
                                calories: e.target.value,
                              })
                            }
                            placeholder="250"
                          />
                        </div>
                        <div className="flex gap-4">
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={newItem.is_vegetarian}
                              onChange={(e) =>
                                setNewItem({
                                  ...newItem,
                                  is_vegetarian: e.target.checked,
                                })
                              }
                            />
                            <span>Vegetarian</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={newItem.is_vegan}
                              onChange={(e) =>
                                setNewItem({
                                  ...newItem,
                                  is_vegan: e.target.checked,
                                })
                              }
                            />
                            <span>Vegan</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={newItem.is_glutten_free}
                              onChange={(e) =>
                                setNewItem({
                                  ...newItem,
                                  is_glutten_free: e.target.checked,
                                })
                              }
                            />
                            <span>Gluten Free</span>
                          </label>
                        </div>
                        <Button onClick={handleAddMenuItem} className="w-full">
                          Add Menu Item
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {menuItems.map((item) => (
                    <Card key={item.id} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h3 className="font-semibold text-lg">
                              {item.name}
                            </h3>
                            <p className="text-orange-600 font-bold text-xl">
                              MWK {parseFloat(item.price).toFixed(2)}
                            </p>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setEditingItem(item)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteMenuItem(item.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <p className="text-gray-600 text-sm mb-3">
                          {item.description}
                        </p>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Category:</span>
                            <Badge variant="outline">
                              {item.category?.name || "Uncategorized"}
                            </Badge>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Prep Time:</span>
                            <span>{item.preparation_time} min</span>
                          </div>
                          {item.calories && (
                            <div className="flex justify-between text-sm">
                              <span>Calories:</span>
                              <span>{item.calories}</span>
                            </div>
                          )}
                          <div className="flex gap-1 flex-wrap">
                            {item.is_vegetarian && (
                              <Badge variant="secondary" className="text-xs">
                                Vegetarian
                              </Badge>
                            )}
                            {item.is_vegan && (
                              <Badge variant="secondary" className="text-xs">
                                Vegan
                              </Badge>
                            )}
                            {item.is_glutten_free && (
                              <Badge variant="secondary" className="text-xs">
                                Gluten Free
                              </Badge>
                            )}
                            <Badge
                              variant={
                                item.is_available ? "default" : "destructive"
                              }
                              className="text-xs"
                            >
                              {item.is_available ? "Available" : "Unavailable"}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
