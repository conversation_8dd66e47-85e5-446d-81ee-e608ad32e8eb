"use client";

import { ArrowUpDown, Delete, Trash } from "lucide-react";
import { Button } from "/@/components/ui/button";

export const columns = [
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    filterable: true,
  },
  { accessorKey: "email", header: "Email", filterable: true },
  { accessorKey: "role", header: "Role", filterable: false },
  {
    id: "actions",
    cell: ({ row }) => {
      const user = row.original;
      return (<div className="flex"><Button size="sm" onClick={() => (window.location.href = `users/${user.id}`)}>
          View
        </Button>
        <Button variant="destructive" size="sm" className="ml-2"><Trash/></Button>
        </div>
        
      );
    },
  },
];
