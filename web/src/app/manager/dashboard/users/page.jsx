import { DataTable } from "@/components/dataTable";
import { columns } from "./colums";
import { Button } from "@/components/ui/button";

async function getData() {
  const users = [
    {
      id: 1,
      name: "Chimwemwe Banda",
      email: "<EMAIL>",
      role: "Waiter",
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      role: "Waiter",
    },
    {
      id: 3,
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      role: "Delivery",
    },
    {
      id: 4,
      name: "Precious <PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      role: "Manager",
    },
    {
      id: 5,
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      role: "Delivery",
    },
  ];

  return users;
}

export default async function UsersPage() {
  const data = await getData();

  return (
    <div className="mx-auto py-5">
      <div className="flex">
        <div className="flex-1">      
          <div className="text-lg font-bold">Users</div>
          <div className="text-sm text-gray-500">
            Manage the users under your domain
          </div>
        </div>
        <Button >New User</Button>
      </div>

      <DataTable columns={columns} data={data} />
    </div>
  );
}
