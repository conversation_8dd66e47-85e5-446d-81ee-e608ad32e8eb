"use client";

import { useState, useEffect } from "react";
import { DataTable } from "@/components/dataTable";
import { columns } from "./colums";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { Plus, Loader2 } from "lucide-react";

export default function UsersPage() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newUser, setNewUser] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    role: "",
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const getAuthHeaders = () => {
    // Try different token storage keys based on user type
    const token =
      localStorage.getItem("managerToken") ||
      localStorage.getItem("accessToken") ||
      localStorage.getItem("adminToken");

    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };

    // Add tenant information for proper routing
    const restaurantId = localStorage.getItem("restaurantId");
    const schemaName = localStorage.getItem("schemaName");
    if (restaurantId) {
      headers["X-Restaurant-ID"] = restaurantId;
    }
    if (schemaName) {
      headers["X-Schema-Name"] = schemaName;
    }

    return headers;
  };

  const getApiUrl = (endpoint) => {
    const baseUrl =
      process.env.NEXT_PUBLIC_BACKEND_URL ||
      process.env.BACKEND_URL ||
      "http://localhost:8000/";
    //"https://dp.technixlabs.org/";

    // For tenant-specific endpoints, we need to use the tenant API routes
    return `${baseUrl}api/tenant/${endpoint}`;
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch(getApiUrl("staff/"), {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        // Transform the data to match the expected format for the DataTable
        const transformedUsers = (data.results || data).map((user) => ({
          id: user.id,
          name: `${user.first_name} ${user.last_name}`.trim() || user.username,
          email: user.email,
          role: user.role,
          phone_number: user.phone_number,
          username: user.username,
        }));
        setUsers(transformedUsers);
      } else {
        console.error(
          "Failed to fetch users:",
          response.status,
          response.statusText
        );
        toast.error(`Failed to fetch users: ${response.status}`);
      }
    } catch (error) {
      console.error("Network error fetching users:", error);
      toast.error("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async () => {
    if (
      !newUser.first_name ||
      !newUser.last_name ||
      !newUser.email ||
      !newUser.role
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(getApiUrl("staff/"), {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(newUser),
      });

      if (response.ok) {
        const createdUser = await response.json();
        toast.success("Staff member added successfully");

        // Show the generated password to the manager
        if (createdUser.password) {
          toast.success(
            `Generated credentials - Username: ${createdUser.username}, Password: ${createdUser.password}`,
            { duration: 10000 }
          );
        }

        setIsAddUserOpen(false);
        setNewUser({
          first_name: "",
          last_name: "",
          email: "",
          phone_number: "",
          role: "",
        });
        fetchUsers(); // Refresh the list
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error("Failed to add user:", response.status, errorData);
        toast.error(`Failed to add user: ${response.status}`);
      }
    } catch (error) {
      console.error("Network error adding user:", error);
      toast.error("Network error");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="mx-auto py-5 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading staff members...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto py-5">
      <div className="flex">
        <div className="flex-1">
          <div className="text-lg font-bold">Staff Management</div>
          <div className="text-sm text-gray-500">
            Manage the staff members in your restaurant
          </div>
        </div>
        <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Staff Member
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Staff Member</DialogTitle>
              <DialogDescription>
                Create a new staff account. Login credentials will be generated
                automatically.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first_name">First Name *</Label>
                  <Input
                    id="first_name"
                    value={newUser.first_name}
                    onChange={(e) =>
                      setNewUser({ ...newUser, first_name: e.target.value })
                    }
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <Label htmlFor="last_name">Last Name *</Label>
                  <Input
                    id="last_name"
                    value={newUser.last_name}
                    onChange={(e) =>
                      setNewUser({ ...newUser, last_name: e.target.value })
                    }
                    placeholder="Enter last name"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) =>
                    setNewUser({ ...newUser, email: e.target.value })
                  }
                  placeholder="Enter email address"
                />
              </div>

              <div>
                <Label htmlFor="phone_number">Phone Number</Label>
                <Input
                  id="phone_number"
                  value={newUser.phone_number}
                  onChange={(e) =>
                    setNewUser({ ...newUser, phone_number: e.target.value })
                  }
                  placeholder="Enter phone number"
                />
              </div>

              <div>
                <Label htmlFor="role">Role *</Label>
                <Select
                  value={newUser.role}
                  onValueChange={(value) =>
                    setNewUser({ ...newUser, role: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="waiter">Waiter</SelectItem>
                    <SelectItem value="delivery">Delivery</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> Username and password will be generated
                  automatically. Make sure to save the credentials when they are
                  displayed.
                </p>
              </div>

              <Button
                onClick={handleAddUser}
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Add Staff Member"
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <DataTable columns={columns} data={users} />
    </div>
  );
}
