"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { toast } from "sonner";
import { Building2, Shield, Search } from "lucide-react";

export function ManagerLoginForm({ className, ...props }) {
  const [restaurantId, setRestaurantId] = useState("");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const login = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      console.log(
        "Attempting manager authentication for restaurant:",
        restaurantId
      );

      // Use the new simplified manager authentication endpoint
      const authRes = await fetch(
        "http://localhost:8000/api/public/manager-auth/",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            restaurant_id: restaurantId,
            username: username,
            password: password,
          }),
        }
      );

      console.log("Manager auth response status:", authRes.status);
      const authData = await authRes.json();
      console.log("Manager auth response data:", authData);

      if (authRes.ok) {
        // Store authentication data
        localStorage.setItem("managerToken", authData.token);
        localStorage.setItem("managerId", authData.user_id);
        localStorage.setItem("managerEmail", authData.email);
        localStorage.setItem("restaurantId", restaurantId);
        localStorage.setItem("restaurantName", authData.restaurant_name);
        localStorage.setItem("schemaName", authData.schema_name);

        toast.success("Login successful! Redirecting to dashboard...");
        router.push("/manager/dashboard/home");
      } else {
        toast.error(authData.error || "Invalid credentials. Please try again.");
      }
    } catch (error) {
      console.error("Login error:", error);
      toast.error("Login failed. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-0 border-2 border-orange-500">
        <CardContent className="grid p-0 md:grid-cols-2">
          <form onSubmit={login} className="p-6 md:p-8">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                  <Building2 className="h-8 w-8 text-orange-600" />
                </div>
                <h1 className="text-2xl font-bold text-orange-900">
                  Restaurant Manager Portal
                </h1>
                <p className="text-muted-foreground text-balance">
                  Access your restaurant management dashboard
                </p>
              </div>
              <div className="grid gap-3">
                <Label htmlFor="restaurantId">Restaurant ID</Label>
                <Input
                  id="restaurantId"
                  type="text"
                  placeholder="e.g., pizza-palace, burger-king"
                  value={restaurantId}
                  onChange={(e) => setRestaurantId(e.target.value)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Enter your restaurant's unique identifier (schema name)
                </p>
              </div>
              <div className="grid gap-3">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="<EMAIL>"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-3">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <a
                    href="#"
                    className="ml-auto text-sm underline-offset-2 hover:underline text-red-600"
                  >
                    Forgot your password?
                  </a>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="Enter your password"
                />
              </div>
              <Button
                type="submit"
                className="w-full bg-red-600 hover:bg-red-700"
                disabled={loading}
              >
                {loading ? "Signing in..." : "Sign in to Dashboard"}
              </Button>
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                  <Shield className="h-4 w-4" />
                  <span>Secure manager access</span>
                </div>
              </div>
            </div>
          </form>
          <div className="grid place-items-center dark:bg-red-900 border-l border-orange-200 bg-orange-50 relative hidden md:block">
            <div className="text-center p-8">
              <Building2 className="h-24 w-24 text-red-600 mx-auto mb-6" />
              <h3 className="text-xl font-semibold text-red-900 mb-2">
                Manage Your Restaurant
              </h3>
              <p className="text-red-700 text-sm">
                Access your restaurant dashboard to manage menus, orders, staff,
                and more.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="text-muted-foreground text-center text-xs text-balance">
        <div className="flex items-center justify-center gap-2">
          <Shield className="h-3 w-3" />
          <span>
            This portal is for authorized restaurant managers only. By
            continuing, you agree to our{" "}
            <a
              href="#"
              className="underline underline-offset-4 hover:text-red-600"
            >
              Terms of Service
            </a>{" "}
            and{" "}
            <a
              href="#"
              className="underline underline-offset-4 hover:text-red-600"
            >
              Privacy Policy
            </a>
            .
          </span>
        </div>
      </div>
    </div>
  );
}
